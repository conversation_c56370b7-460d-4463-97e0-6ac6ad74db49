/* eslint-disable indent */
const sns = require('../lib/sns');
const helper = require('../lib/prompt-helper');
/*
As no real interface implementation in NodeJS, in order to simulate it, new class should follow below
blueprint:

const PromptInterface = {
    getCost: function (promptTokens, outputTokens, model) {},
    executePrompt: async function ({ prompt, image_urls, temperature, response_format, model }) { output, promptTokens, outputTokens, cost, processingTime, errorMessage }
}
*/

/*
Usage example:
const { OpenAI, ClaudeAPI, executePrompt } = require('../lib/prompt');
const openaiClient = require('../lib/openai-client');

(async() => {
    let client = openaiClient.getOpenaiClientForPoseVerification();
    const openAi = new OpenAI(client, 'OpenAIModel');
    const claudeApi = new ClaudeAPI('ClaudeApiClient', 'ClaudeApiModel');
    const { output, promptTokens, outputTokens, cost, processingTime, errorMessage } = await openAi.executePrompt(params)
})();

*/

const getOutputString = (output) => {
  let result = '';
  try {
    const outputString = Array.isArray(output) ? output.join('') : output;
    // Sometimes no double quote around ouput, so we need to add it,
    // updated the code to took only string inside array and added double quoted around ouput to make valid json
    const startIndex = outputString.indexOf('[');
    const endIndex = outputString.indexOf(']');
    if (startIndex !== -1 && endIndex !== -1 && startIndex < endIndex) {
      result = `{ "output": ${outputString.substring(
        startIndex,
        endIndex + 1,
      )} }`;
    }
  } catch (error) {
    console.log(
      'Order of [ and ] is wrong or they are not found in the output',
    );
  }
  return result;
};

class OpenAI {
    constructor(client, model) {
        this.client = client;
        this.model = model;
    }

    getCost(promptTokens, outputTokens, model) {
        if (model == 'gpt-3.5-turbo-1106') {
            let inputCost = 0.001 * (promptTokens / 1000);
            let outputCost = 0.002 * (outputTokens / 1000);
            return inputCost + outputCost;
        }
        if (model == 'gpt-3.5-turbo-0125') {
            let inputCost = 0.0005 * (promptTokens / 1000);
            let outputCost = 0.0015 * (outputTokens / 1000);
            return inputCost + outputCost;
        }
        if (model == 'gpt-4') {
            let inputCost = 0.03 * (promptTokens / 1000);
            let outputCost = 0.06 * (outputTokens / 1000);
            return inputCost + outputCost;
        }
        if (['gpt-4-vision-preview', 'gpt-4-1106-preview', 'gpt-4-turbo-preview'].includes(model)) {
            let inputCost = 0.01 * (promptTokens / 1000);
            let outputCost = 0.03 * (outputTokens / 1000);
            return inputCost + outputCost;
        }
        if (model === 'gpt-4o') {
            let inputCost = 0.005 * (promptTokens / 1000);
            let outputCost = 0.015 * (outputTokens / 1000);
            return inputCost + outputCost;
        }
        if (model === 'gpt-4o-mini') {
            let inputCost = 0.15 * (promptTokens / 1000000);
            let outputCost = 0.6 * (outputTokens / 1000000);
            return inputCost + outputCost;
        }
        if (model.includes('ft:gpt-4o-mini')) {
          let inputCost = 0.3 * (promptTokens / 1000000);
          let outputCost = 1.2 * (outputTokens / 1000000);
          return inputCost + outputCost;
        }
        return 0;
    }

    async executePrompt(params) {
        let model = this.model;
        if (params.model) {
            model = params.model
        }
        let max_tokens;
        if (params.max_tokens) {
            max_tokens = params.max_tokens;
        } else {
            if (model == 'gpt-4-vision-preview') {
                max_tokens = 500;
            }
        }
        const { prompt, image_urls, temperature, response_format, systemPrompt, sendImageKey } = params;

        const content = [
            {
                type: 'text',
                text: prompt,
            }
        ]
        if (image_urls && image_urls.length > 0) {
          for (let index = 0; index < image_urls.length; index++) {
            if(sendImageKey){
              content.push({
                type: 'text',
                text: `key for the next image: Photo ${index + 1}`,
              });
            }
            content.push({
                type: 'image_url',
                image_url: {
                    url: image_urls[index],
                    detail: 'low',
                },
            });
          }
        }
        const messages = [
            {
                role: 'user',
                content: content,
            }
        ]

        if (systemPrompt) {
          messages.unshift({
            role: 'system',
            content: systemPrompt,
          });
        }

        let client = this.client;
        let getCost = this.getCost;
        async function helper() {
            let output, promptTokens, outputTokens, cost, errorMessage, promptId;
            try {
                const response = await client.chat.completions.create({
                    model,
                    messages,
                    max_tokens,
                    temperature,
                    response_format,
                });
                output = response.choices[0].message.content;
                promptTokens = response.usage.prompt_tokens;
                outputTokens = response.usage.completion_tokens;
                cost = getCost(promptTokens, outputTokens, model);
                promptId = response.id;
            } catch (err) {
                console.log(err)
                if (err && err.message) {
                    errorMessage = err.message;
                }
            }
            return { output, promptTokens, outputTokens, cost, errorMessage, promptId };
        }

        let output, promptTokens, outputTokens, cost, processingTime, errorMessage, promptId;
        const start = new Date().getTime();
        ({ output, promptTokens, outputTokens, cost, errorMessage, promptId } = await helper());
        if (errorMessage) {
            const lower = errorMessage.trim().toLowerCase();
            if (lower.includes('invalid image') || lower.includes('unsupported image')) {
                console.log('retrying on invalid image error');
                ({ output, promptTokens, outputTokens, cost, errorMessage } = await helper());
            }
        }
        const end = new Date().getTime();
        processingTime = end - start;

        if (errorMessage && errorMessage.includes('Rate limit')) {
            await sns.publishAlarm(`OpenAI rate limit error: ${errorMessage}`); // err.message was undefined
        }

        return {
          output,
          promptTokens,
          outputTokens,
          cost: cost || 0,
          processingTime,
          errorMessage,
          promptId: promptId || '',
          payloadOnError: errorMessage ? JSON.stringify({
              model,
              messages,
              max_tokens,
              temperature,
              response_format,
          }) : undefined,
        };
    }
}
class ClaudeAPI {
    constructor(client, model) {
        this.client = client;
        this.model = model;
    }

    getCost(promptTokens, outputTokens, model) {
        const sonnetModels = [
          'claude-3-sonnet',
          'claude-3-5-sonnet',
          'claude-3-7-sonnet',
          'claude-sonnet-4',
        ];

        if (sonnetModels.some(name => model.includes(name))) {
          let inputCost = 0.003 * (promptTokens / 1000);
          let outputCost = 0.015 * (outputTokens / 1000);
          return inputCost + outputCost;
        }
        if (['claude-2.0', 'claude-2.1'].includes(model)) {
            let inputCost = 0.008 * (promptTokens / 1000);
            let outputCost = 0.024 * (outputTokens / 1000);
            return inputCost + outputCost;
        }
        if (model.includes('claude-3-opus')) {
            let inputCost = 0.015 * (promptTokens / 1000);
            let outputCost = 0.075 * (outputTokens / 1000);
            return inputCost + outputCost;
        }
        if (model.includes('claude-3-haiku')) {
            let inputCost = 0.00025 * (promptTokens / 1000);
            let outputCost = 0.00125 * (outputTokens / 1000);
            return inputCost + outputCost;
        }
        if (model.includes('claude-3-5-haiku')) {
            let inputCost = (promptTokens * 0.80) / 1000000;
            let outputCost = (outputTokens * 4.00) / 1000000;
            return inputCost + outputCost;
        }
        return 0;
    }

    async executePrompt(params) {
        let model = this.model;
        if (params.model) {
            model = params.model
        }
        let output, promptTokens, outputTokens, cost, processingTime, errorMessage, promptId;
        let max_tokens = 1024;
        const { prompt, image_urls } = params;

        const content = [
            {
                type: 'text',
                text: prompt,
            }
        ]
        if (image_urls && image_urls.length > 0) {
            for (const imageUrl of image_urls) {
                content.push({
                    type: 'image',
                    source: {
                        type: 'url',
                        url: imageUrl,
                    },
                });
            }
        }

        const messages = [
            {
                role: 'user',
                content: content,
            }
        ]

        console.log(JSON.stringify({
            model,
            messages,
            max_tokens,
        }))

        const start = new Date().getTime();
        try {
            const response = await this.client.messages.create({
                model,
                messages,
                max_tokens,
            });
            console.log(JSON.stringify(response, null, 2));
            output = response.content[0].text;
            promptTokens = response.usage.input_tokens;
            outputTokens = response.usage.output_tokens;
            cost = this.getCost(promptTokens, outputTokens, model);
            promptId = response.id;
        } catch (err) {
            console.log(err)
            if (err && err.message) {
                errorMessage = err.message;
            }
        }
        const end = new Date().getTime();
        processingTime = end - start;

        return {
          output,
          promptTokens,
          outputTokens,
          cost: cost || 0,
          processingTime,
          errorMessage,
          promptId: promptId || '',
          payloadOnError: errorMessage ? JSON.stringify({
              model,
              messages,
              max_tokens,
          }) : undefined,
        };
      }
}

class ReplicateAPI {
  constructor(client, model) {
    this.client = client;
    this.model = model;
  }

  getCost(promptTokens, outputTokens, model) {
    if (['meta/meta-llama-3-70b-instruct'].includes(model)) {
      let inputCost = 0.00_00_00_65 * promptTokens;
      let outputCost = 0.00_00_0275 * outputTokens;
      return inputCost + outputCost;
    }
    return 0;
  }

  async executePrompt(params) {
    let model = this.model;
    if (params.model) {
      model = params.model;
    }
    let output, promptTokens, outputTokens, cost, processingTime, errorMessage, promptId;
    const max_tokens = 1024;
    const { prompt, response_format } = params;

    const input = {
      prompt,
      max_tokens,
      prompt_template:
        '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are a helpful assistant<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>',
    };

    // console.log(
    //   JSON.stringify({
    //     model,
    //     input,
    //     max_tokens,
    //   }),
    // );

    const start = new Date().getTime();
    try {
      const executionPromise = new Promise((resolve, reject) => {
        this.client.createPrediction(this.model, input)
          .then((prediction) => {
            promptId = prediction.id;
            if (!prediction.error) {
              this.client.getPrediction(prediction.id)
                .then((response) => {
                  if (response) {
                    if (response_format?.type === 'json_object') {
                      output = getOutputString(response.output) || '{ "output": [] }';
                    } else {
                      output = response.output.join('');
                    }
                    promptTokens = response.metrics.input_token_count;
                    outputTokens = response.metrics.output_token_count;
                    cost = this.getCost(promptTokens, outputTokens, model);
                  }
                  resolve();
                })
                .catch((err) => {
                  reject(err);
                });
            } else {
              reject(new Error(prediction.error));
            }
          })
          .catch((err) => {
            reject(err);
          });
      });

      if (params.shouldTimeout) {
        const timeout = new Promise((resolve, reject) => {
          setTimeout(() => {
            reject(new Error('Timeout'));
          }, 59000);
        });

        await Promise.race([executionPromise, timeout]);
      } else {
        await executionPromise;
      }
    } catch (err) {
      errorMessage = err.message;
    }

    const end = new Date().getTime();
    processingTime = end - start;

    return {
      output,
      promptTokens,
      outputTokens,
      cost: cost || 0,
      processingTime,
      errorMessage,
      promptId: promptId || '',
      payloadOnError: errorMessage ? JSON.stringify(input) : undefined,
    };
  }
}

class GroqAPI {
  constructor(client, model) {
    this.client = client;
    this.model = model;
  }

  // On-demand pricing for groq is $0.59 per 1 Million tokens for input and $0.79 per 1 Million tokens for output
  getCost(promptTokens, outputTokens, model) {
    if (['llama3-70b-8192'].includes(model)) {
      let inputCost = 0.00_00_00_59 * promptTokens;
      let outputCost = 0.00_00_00_79 * outputTokens;
      return inputCost + outputCost;
    }
    return 0;
  }

  async executePrompt(params) {
    let model = params.model || this.model;
    let retries = 1;
    let output, promptTokens, outputTokens, cost, processingTime, errorMessage, promptId;
    const max_tokens = 8000;
    const { prompt, temperature, response_format } = params;

    const input = {
      messages: [{
        role: 'user',
        content: prompt,
      }],
      model,
      temperature,
      max_tokens,
      response_format: response_format?.type === 'json_object' ? response_format : undefined,
      stream: response_format?.type !== 'json_object', // Groq doesn't have stream support for json output
    };

    if (response_format?.type === 'json_object') {
      input.messages.unshift({
        role: 'system',
        content: 'You are a helpful assistant who provides all responses in JSON format',
      });
    }

    const start = new Date().getTime();
    const executeChatCompletion = async () =>
      new Promise((resolve, reject) => {
        this.client.chat.completions
          .create(input)
          .then(async (response) => {
            let content = '';
            let usage;
            if (input.stream) {
              for await (const chunk of response) {
                content += chunk.choices[0]?.delta?.content || '';
                if (!promptId && chunk.id) {
                  promptId = chunk.id;
                }
                if (chunk.choices[0]?.finish_reason === 'stop') {
                  usage = chunk.x_groq?.usage;
                }
              }
            } else {
              promptId = response.id;
              usage = response.usage;
              content = response.choices[0]?.message?.content || JSON.stringify({ output: [] });
            }
            if (usage) {
              promptTokens = usage.prompt_tokens;
              outputTokens = usage.completion_tokens;
              cost = this.getCost(promptTokens, outputTokens, model);
            }
            output = content;
            resolve();
          })
          .catch((err) => {
            if (err.status === 400 && retries > 0) {
              retries -= 1;
              executeChatCompletion().then(resolve).catch(reject);
            } else {
              reject(err);
            }
          });
      });

    try {
      const executionPromise = await executeChatCompletion();
      if (params.shouldTimeout) {
        const timeout = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('Timeout'));
          }, 59000);
        });

        await Promise.race([executionPromise, timeout]);
      } else {
        await executionPromise;
      }
    } catch (err) {
      errorMessage = err.message;
    }

    const end = new Date().getTime();
    processingTime = end - start;

    return {
      output,
      promptTokens,
      outputTokens,
      cost: cost || 0,
      processingTime,
      errorMessage,
      promptId: promptId || '',
      payloadOnError: errorMessage ? JSON.stringify(input) : undefined,
    };
  }
}

// We have more generic implementation of TogetherAI in: https://github.com/boo-world/backend/pull/142
// Deepseek model specific implementation
class DeepseekTogetherAI {
  constructor(client, model) {
    this.client = client;
    this.model = model;
  }

  getCost(promptTokens, outputTokens, model) {
    if (model == 'deepseek-ai/DeepSeek-R1') {
      let inputCost = 3 * (promptTokens / 1000000);
      let outputCost = 7 * (outputTokens / 1000000);
      return inputCost + outputCost;
    }
    if (model == 'deepseek-ai/DeepSeek-V3') {
      let inputCost = 1.25 * (promptTokens / 1000000);
      let outputCost = 1.25 * (outputTokens / 1000000);
      return inputCost + outputCost;
    }
    return 0;
  }

  async executePrompt(params) {
    const start = Date.now();
    const model = params.model || this.model;
    const { prompt, temperature, max_tokens, response_format } = params;
    const messages = [{ role: 'user', content: prompt }];

    let output = '',
      reasoning_content = '',
      promptTokens = 0,
      outputTokens = 0,
      cost = 0,
      errorMessage,
      promptId = '';

    try {
      const response = await this.client.chat.completions.create({
        messages,
        model,
        repetition_penalty: 1,
        stop: ['<｜end▁of▁sentence｜>'],
        temperature,
        max_tokens,
        stream: false,
      });

      const rawOutput = response.choices?.[0]?.message?.content || '';
      if (model === 'deepseek-ai/DeepSeek-R1') {
        [reasoning_content, output] = rawOutput.split('</think>');
        if (reasoning_content) {
          reasoning_content = reasoning_content.replace('<think>', '');
        }
      } else {
        output = rawOutput;
      }
      promptTokens = response.usage?.prompt_tokens || 0;
      outputTokens = response.usage?.completion_tokens || 0;
      cost = this.getCost(promptTokens, outputTokens, model);
      promptId = response.id || '';
    } catch (err) {
      console.log('DeepseekTogetherAI Error:', err);
      errorMessage = err.message || 'Unknown error';
    }

    return {
      output,
      reasoning_content,
      promptTokens,
      outputTokens,
      cost,
      processingTime: Date.now() - start,
      errorMessage,
      promptId,
      payloadOnError: errorMessage
        ? JSON.stringify({ model, messages, max_tokens, temperature, response_format })
        : undefined,
    };
  }
}

module.exports = {
  OpenAI,
  ClaudeAPI,
  ReplicateAPI,
  GroqAPI,
  DeepseekTogetherAI,
};
